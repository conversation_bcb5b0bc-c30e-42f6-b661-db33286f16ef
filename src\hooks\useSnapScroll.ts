import { useEffect, useRef, useCallback } from "react";

interface UseSnapScrollOptions {
  sectionIds: string[];
  enabled?: boolean;
  onSectionChange?: (sectionId: string, index: number) => void;
  wheelSensitivity?: number;
  touchSensitivity?: number;
}

/**
 * Hook to implement snap scroll functionality similar to NCVC website
 * Provides mouse wheel navigation between sections with smooth transitions
 */
export const useSnapScroll = ({
  sectionIds,
  enabled = true,
  onSectionChange,
  wheelSensitivity = 1,
  touchSensitivity = 50,
}: UseSnapScrollOptions) => {
  const currentSectionIndex = useRef(0);
  const isScrolling = useRef(false);
  const scrollTimeout = useRef<NodeJS.Timeout | null>(null);
  const touchStartY = useRef(0);

  // Scroll to specific section
  const scrollToSection = useCallback(
    (index: number, smooth = true) => {
      if (index < 0 || index >= sectionIds.length) return;

      const sectionId = sectionIds[index];
      const section = document.getElementById(sectionId);

      if (section) {
        currentSectionIndex.current = index;

        // Use scrollIntoView for better browser compatibility
        section.scrollIntoView({
          behavior: smooth ? "smooth" : "auto",
          block: "start",
          inline: "nearest",
        });

        // Call callback if provided
        if (onSectionChange) {
          onSectionChange(sectionId, index);
        }
      }
    },
    [sectionIds, onSectionChange]
  );

  // Handle wheel events for mouse scroll navigation
  const handleWheel = useCallback(
    (event: WheelEvent) => {
      if (!enabled || isScrolling.current) return;

      // Allow normal scrolling within sections if they have scrollable content
      const target = event.target as Element;
      if (target && target.closest(".scrollable-content")) {
        return;
      }

      event.preventDefault();

      const delta = event.deltaY * wheelSensitivity;
      const threshold = 30; // Increased threshold for better control

      if (Math.abs(delta) < threshold) return;

      isScrolling.current = true;

      // Clear existing timeout
      if (scrollTimeout.current) {
        clearTimeout(scrollTimeout.current);
      }

      if (delta > 0) {
        // Scroll down - next section
        const nextIndex = Math.min(
          currentSectionIndex.current + 1,
          sectionIds.length - 1
        );
        if (nextIndex !== currentSectionIndex.current) {
          console.log("Scrolling to next section:", sectionIds[nextIndex]);
          scrollToSection(nextIndex);
        }
      } else {
        // Scroll up - previous section
        const prevIndex = Math.max(currentSectionIndex.current - 1, 0);
        if (prevIndex !== currentSectionIndex.current) {
          console.log("Scrolling to previous section:", sectionIds[prevIndex]);
          scrollToSection(prevIndex);
        }
      }

      // Reset scrolling flag after animation
      scrollTimeout.current = setTimeout(() => {
        isScrolling.current = false;
      }, 1200); // Increased timeout for smoother experience
    },
    [enabled, wheelSensitivity, scrollToSection, sectionIds.length]
  );

  // Handle touch events for mobile navigation
  const handleTouchStart = useCallback(
    (event: TouchEvent) => {
      if (!enabled) return;
      touchStartY.current = event.touches[0].clientY;
    },
    [enabled]
  );

  const handleTouchEnd = useCallback(
    (event: TouchEvent) => {
      if (!enabled || isScrolling.current) return;

      const touchEndY = event.changedTouches[0].clientY;
      const deltaY = touchStartY.current - touchEndY;

      if (Math.abs(deltaY) < touchSensitivity) return;

      event.preventDefault();
      isScrolling.current = true;

      if (deltaY > 0) {
        // Swipe up - next section
        const nextIndex = Math.min(
          currentSectionIndex.current + 1,
          sectionIds.length - 1
        );
        if (nextIndex !== currentSectionIndex.current) {
          scrollToSection(nextIndex);
        }
      } else {
        // Swipe down - previous section
        const prevIndex = Math.max(currentSectionIndex.current - 1, 0);
        if (prevIndex !== currentSectionIndex.current) {
          scrollToSection(prevIndex);
        }
      }

      // Reset scrolling flag after animation
      scrollTimeout.current = setTimeout(() => {
        isScrolling.current = false;
      }, 1200);
    },
    [enabled, touchSensitivity, scrollToSection, sectionIds.length]
  );

  // Handle keyboard navigation
  const handleKeyDown = useCallback(
    (event: KeyboardEvent) => {
      if (!enabled || isScrolling.current) return;

      switch (event.key) {
        case "ArrowDown":
        case "PageDown":
          event.preventDefault();
          const nextIndex = Math.min(
            currentSectionIndex.current + 1,
            sectionIds.length - 1
          );
          scrollToSection(nextIndex);
          break;
        case "ArrowUp":
        case "PageUp":
          event.preventDefault();
          const prevIndex = Math.max(currentSectionIndex.current - 1, 0);
          scrollToSection(prevIndex);
          break;
        case "Home":
          event.preventDefault();
          scrollToSection(0);
          break;
        case "End":
          event.preventDefault();
          scrollToSection(sectionIds.length - 1);
          break;
      }
    },
    [enabled, scrollToSection, sectionIds.length]
  );

  // Get current section index
  const getCurrentSectionIndex = useCallback(() => {
    return currentSectionIndex.current;
  }, []);

  // Navigate to specific section by ID
  const navigateToSection = useCallback(
    (sectionId: string) => {
      const index = sectionIds.indexOf(sectionId);
      if (index !== -1) {
        scrollToSection(index);
      }
    },
    [sectionIds, scrollToSection]
  );

  // Detect current section based on scroll position
  const detectCurrentSection = useCallback(() => {
    const scrollPosition = window.scrollY + window.innerHeight / 2;

    for (let i = sectionIds.length - 1; i >= 0; i--) {
      const section = document.getElementById(sectionIds[i]);
      if (section) {
        const sectionTop = section.offsetTop;
        const sectionHeight = section.offsetHeight;

        if (
          scrollPosition >= sectionTop &&
          scrollPosition <= sectionTop + sectionHeight
        ) {
          if (currentSectionIndex.current !== i) {
            currentSectionIndex.current = i;
            if (onSectionChange) {
              onSectionChange(sectionIds[i], i);
            }
          }
          break;
        }
      }
    }
  }, [sectionIds, onSectionChange]);

  useEffect(() => {
    if (!enabled) return;

    // Add scroll listener for section detection
    const handleScroll = () => {
      if (!isScrolling.current) {
        detectCurrentSection();
      }
    };

    // Add event listeners
    window.addEventListener("wheel", handleWheel, { passive: false });
    window.addEventListener("touchstart", handleTouchStart, { passive: true });
    window.addEventListener("touchend", handleTouchEnd, { passive: false });
    window.addEventListener("keydown", handleKeyDown);
    window.addEventListener("scroll", handleScroll, { passive: true });

    // Initialize to first section
    scrollToSection(0, false);

    return () => {
      // Cleanup
      window.removeEventListener("wheel", handleWheel);
      window.removeEventListener("touchstart", handleTouchStart);
      window.removeEventListener("touchend", handleTouchEnd);
      window.removeEventListener("keydown", handleKeyDown);
      window.removeEventListener("scroll", handleScroll);

      if (scrollTimeout.current) {
        clearTimeout(scrollTimeout.current);
      }
    };
  }, [
    enabled,
    handleWheel,
    handleTouchStart,
    handleTouchEnd,
    handleKeyDown,
    scrollToSection,
    detectCurrentSection,
  ]);

  return {
    scrollToSection,
    getCurrentSectionIndex,
    navigateToSection,
    currentSectionIndex: currentSectionIndex.current,
  };
};

export default useSnapScroll;
