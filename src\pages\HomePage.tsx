import Hero from "../components/home/<USER>";
import Services from "../components/home/<USER>";
import PortalStatistics from "../components/home/<USER>";
// @ts-ignore
import { MapProvider } from "../components/mapFolder/MapContext.js";
import HomeLayout from "../components/layouts/HomeLayout";
import { useEffect } from "react";
import { scrollToTop } from "../utils/helpers";
import Map2 from "../components/home/<USER>/Map2.js";
import BulletNavigation from "../components/common/BulletNavigation";
import useFullScreenSections from "../hooks/useFullScreenSections";
import { useTranslation } from "react-i18next";
import { FaHome, FaMap, FaCogs, FaChartBar } from "react-icons/fa";

/**
 * HomePage component that uses the HomeLayout
 * This page includes the Hero, Map, Services, and Statistics sections with bullet navigation
 */
export default function HomePage() {
  const { t } = useTranslation();

  // Section IDs for bullet navigation
  const sectionIds = [
    "hero-section",
    "map-section",
    "services-section",
    "statistics-section",
  ];

  // Make sections full-screen height
  useFullScreenSections({
    sectionIds,
    enabled: true,
    offset: 0, // No offset for snap scroll - full viewport height
  });

  // Scroll to top when the component mounts and setup snap scroll
  useEffect(() => {
    scrollToTop();

    // Add snap scroll classes
    document.body.classList.add("snap-scroll-active");
    document.documentElement.classList.add("snap-scroll-active");

    // Cleanup on unmount
    return () => {
      document.body.classList.remove("snap-scroll-active");
      document.documentElement.classList.remove("snap-scroll-active");
    };
  }, []);

  // Define sections for bullet navigation
  const bulletSections = [
    {
      id: "hero-section",
      label: t("navigation.home", "الرئيسية"),
      icon: <FaHome />,
    },
    {
      id: "map-section",
      label: t("navigation.map", "الخريطة"),
      icon: <FaMap />,
    },
    {
      id: "services-section",
      label: t("navigation.services", "الخدمات"),
      icon: <FaCogs />,
    },
    {
      id: "statistics-section",
      label: t("navigation.statistics", "الإحصائيات"),
      icon: <FaChartBar />,
    },
  ];

  return (
    <HomeLayout>
      {/* Bullet Navigation */}
      <BulletNavigation sections={bulletSections} />

      {/* Hero Section */}
      <section id="hero-section">
        <Hero />
      </section>

      {/* Map Section */}
      <section id="map-section">
        <MapProvider>
          <div>
            <Map2 />
          </div>
        </MapProvider>
      </section>

      {/* Services Section */}
      <section id="services-section">
        <Services />
      </section>

      {/* Statistics Section */}
      <section id="statistics-section">
        <PortalStatistics />
      </section>
    </HomeLayout>
  );
}
